using System.Diagnostics;
using deeplab_systems.Models;
using deeplab_systems.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace deeplab_systems.Controllers
{
    public class HomeController : Controller
    {
        private readonly IEmailService _emailService;
        private readonly EmailSettings _emailSettings;

        public HomeController(IEmailService emailService, IOptions<EmailSettings> emailSettings)
        {
            _emailService = emailService;
            _emailSettings = emailSettings.Value;
        }

        public IActionResult Index()
        {
            return View(new ContactViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SendQuote(ContactViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View("Index", model);
            }

            try
            {
                string subject = $"New Quote Request from {model.Name}";
                string body = $@"
                    <p><strong>Name:</strong> {model.Name}</p>
                    <p><strong>Email:</strong> {model.Email}</p>
                    <p><strong>Company:</strong> {model.Company ?? "N/A"}</p>
                    <p><strong>Message:</strong></p>
                    <p>{model.Message}</p>
                ";

                await _emailService.SendEmailAsync(_emailSettings.SenderEmail, subject, body);

                TempData["Message"] = "Your quote request has been sent successfully. We will get back to you shortly!";
                TempData["MessageType"] = "success";
                return RedirectToAction("Index", "Home", new
                { contact = "contact" }); // Redirect to home with anchor
            }
            catch (Exception ex)
            {
                TempData["Message"] = $"Failed to send your request. Please try again later. Error: {ex.Message}";
                TempData["MessageType"] = "error";
                return View("Index", model);
            }
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
