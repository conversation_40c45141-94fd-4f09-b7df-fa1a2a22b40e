@model deeplab_systems.Models.ContactViewModel

<div class="contact-form-container">
    @if (TempData["Message"] != null)
    {
        <div class="alert @(TempData["MessageType"] == "success" ? "alert-success" : "alert-danger") message-blurview">
            @TempData["Message"]
            <button type="button" class="close-alert" onclick="this.parentElement.style.display='none';">✖</button>
        </div>
    }

    <form asp-controller="Home" asp-action="SendQuote" method="post" class="contact-form">
        <div asp-validation-summary="ModelOnly" class="text-danger validation-summary"></div>

        <div class="form-group slide-up delay-100">
            <label asp-for="Name"></label>
            <input asp-for="Name" class="form-control" placeholder="Your Name" />
            <span asp-validation-for="Name" class="text-danger"></span>
        </div>

        <div class="form-group slide-up delay-150">
            <label asp-for="Email"></label>
            <input asp-for="Email" class="form-control" placeholder="<EMAIL>" />
            <span asp-validation-for="Email" class="text-danger"></span>
        </div>

        <div class="form-group slide-up delay-200">
            <label asp-for="Company"></label>
            <input asp-for="Company" class="form-control" placeholder="Your Company (Optional)" />
            <span asp-validation-for="Company" class="text-danger"></span>
        </div>

        <div class="form-group slide-up delay-250">
            <label asp-for="Message"></label>
            <textarea asp-for="Message" class="form-control" rows="5" placeholder="Tell us about your project..."></textarea>
            <span asp-validation-for="Message" class="text-danger"></span>
        </div>

        <button type="submit" class="btn btn-submit scale-in delay-300">Send Quote</button>
    </form>
</div>